import { NextResponse } from 'next/server'

export async function POST() {
  const orchestratorBase = process.env.CALL_ORCHESTRATOR_URL
  const voiceRouterHttp = process.env.VOICE_ROUTER_HTTP_URL

  // Try Call Orchestrator first
  if (orchestratorBase) {
    try {
      const res = await fetch(`${orchestratorBase.replace(/\/$/, '')}/api/v1/calls`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      })
      if (res.ok) {
        const json = await res.json()
        return NextResponse.json(json, { status: 201 })
      }
    } catch {
      // fall through to direct voice-router fallback
    }
  }

  // Fallback: create session directly on voice-router (no DB call record)
  if (voiceRouterHttp) {
    try {
      const res = await fetch(`${voiceRouterHttp.replace(/\/$/, '')}/internal/sessions`, {
        method: 'POST',
      })
      if (!res.ok) {
        const text = await res.text().catch(() => '')
        return NextResponse.json({ error: `Voice Router error (${res.status}): ${text}` }, { status: 502 })
      }
      const { mediaSessionId } = await res.json() as { mediaSessionId: string }
      const now = new Date().toISOString()
      // Return a shape similar to call-orchestrator response
      return NextResponse.json({ id: crypto.randomUUID(), status: 'QUEUED', createdAt: now, endedAt: null, mediaSessionId }, { status: 201 })
    } catch (e) {
      const msg = e instanceof Error ? e.message : 'Failed to reach Voice Router'
      return NextResponse.json({ error: msg }, { status: 500 })
    }
  }

  return NextResponse.json({ error: 'No CALL_ORCHESTRATOR_URL or VOICE_ROUTER_HTTP_URL configured' }, { status: 500 })
}

