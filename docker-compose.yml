services:

  # ------------------------------------------------------------------
  # Cortexa Microservices Platform
  # ------------------------------------------------------------------
  user-management:
    image: cortexa/user-management:latest
    container_name: cortexa-user-management
    ports:
      - "8000:8000"
    environment:
      - HOST=0.0.0.0
      - PORT=8000
    extra_hosts:
      # This is needed for the container to connect to Supabase on the host
      - "host.docker.internal:host-gateway"
    networks:
      - internal
      - api-gateway
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.user-management.rule=Host(`localhost`) && PathPrefix(`/users`)"
      - "traefik.http.routers.user-management.entrypoints=web"
      - "traefik.http.routers.user-management.middlewares=jwt-auth-role-administrator@file"
      - "traefik.http.services.user-management.loadbalancer.server.port=8000"

  call-orchestrator:
    image: cortexa/call-orchestrator:latest
    container_name: cortexa-call-orchestrator
    ports:
      - "8002:8002"
    environment:
      - HOST=0.0.0.0
      - PORT=8002
      - DATABASE_URL=****************************************/cortexa
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - VOICE_ROUTER_URL=http://voice-router:3000
    depends_on:
      - kafka
    networks:
      - internal
      - api-gateway

  voice-router:
    build:
      context: ./services.voice-router
      dockerfile: Dockerfile
    image: cortexa/voice-router:latest
    container_name: cortexa-voice-router
    ports:
      - "3000:3000"
      - "40000-49999:40000-49999/udp"
      - "40000-49999:40000-49999/tcp"
    environment:
      - LISTEN_HOST=0.0.0.0
      - LISTEN_PORT=3000
      - MEDIASOUP_WORKERS=4
      - MEDIASOUP_LISTEN_IP=0.0.0.0
      - MEDIASOUP_ANNOUNCED_IP=*************
    networks:
      - internal
      - api-gateway

  # =========== sidecars ===========

  # ------------------------------------------------------------------
  # Traefik Reverse Proxy
  #
  # What: Reverse proxy for all services, provides load balancing, SSL termination, and JWT authentication
  # Why: Centralized point of entry for all services, simplifies service discovery and routing
  # ------------------------------------------------------------------

  traefik:
    image: "traefik:v2.11"
    container_name: cortexa-traefik
    ports:
      - "80:80"
      - "8080:8080"
    command:
      # Enable the API dashboard (for debugging)
      - "--api.insecure=true"
      # Enable the Docker provider to read service labels
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      # Point to the dynamic configuration file for our middleware
      - "--providers.file.directory=/configuration/"
      - "--providers.file.watch=true"
      # Define the entrypoint for web traffic
      - "--entrypoints.web.address=:80"
      # Enable the experimental plugin feature and specify the JWT middleware
      - "--experimental.plugins.jwt.modulename=github.com/agilezebra/jwt-middleware"
      - "--experimental.plugins.jwt.version=v1.3.2"
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock:ro"
      - "./infra/gateway:/configuration/"
    networks:
      - api-gateway

  # ------------------------------------------------------------------
  # Kafka and Zookeeper
  #
  # What: Message queue for inter-service communication
  # Why: Decouples services, enables asynchronous communication, and provides fault tolerance
  # ------------------------------------------------------------------

  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: cortexa-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - internal

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: cortexa-kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
    healthcheck:
      test: ["CMD", "kafka-topics", "--bootstrap-server", "localhost:9092", "--list"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - internal

  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: cortexa-kafka-ui
    depends_on:
      - kafka
    ports:
      - "8081:8080"
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
    networks:
      - internal
      - api-gateway

  # ------------------------------------------------------------------
  # Database
  #
  # What: PostgreSQL database for persistent data storage
  # Why: Centralized data storage for all services
  # ------------------------------------------------------------------
  postgres:
    image: postgres:15.4-alpine
    container_name: cortexa-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=cortexa
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - internal

  # ------------------------------------------------------------------
  # Monitoring
  #
  # What: Observability tools for monitoring and tracing
  # Why: Provides insights into service health, performance, and dependencies
  # ------------------------------------------------------------------

  prometheus:
    image: prom/prometheus:v2.47.0
    container_name: cortexa-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./infra/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - internal

  grafana:
    image: grafana/grafana:10.1.0
    container_name: cortexa-grafana

    volumes:
      - grafana_data:/var/lib/grafana
      #- ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      #- ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    networks:
      - internal
      - api-gateway


volumes:
  prometheus_data:
  grafana_data:
  postgres_data:


networks:
  api-gateway:
    driver: bridge
  internal:
    driver: bridge
